@import 'bootstrap/bootstrap';
@import 'bootstrap/variables';

$font-primary: 'Overpass',Arial, sans-serif;
$font-secondary: '<PERSON><PERSON>',Arial, sans-serif;

$white: #fff;
$black: #000;
$darken: #4a5562;

$primary: #f86f2d;
$secondary: #fa8f3d;
$tertiary: #faaa3a;
$quarternary: #7cbd1e;


@mixin border-radius($radius) {
  -webkit-border-radius: $radius;
     -moz-border-radius: $radius;
      -ms-border-radius: $radius;
          border-radius: $radius;
}

@mixin transition($transition) {
    -moz-transition:    all $transition ease;
    -o-transition:      all $transition ease;
    -webkit-transition: all $transition ease;
    -ms-transition: 		all $transition ease;
    transition:         all $transition ease;
}

html {
	// overflow-x: hidden;
}
body {
	font-family: $font-primary;
	background: $white;
	font-size: 16px;
	line-height: 1.8;
	font-weight: 300;
	color: lighten($black,50%);
	&.menu-show {
		overflow: hidden;
		position: fixed;
		height: 100%;
		width: 100%;
	}
}
span{
	color: lighten($black,75%);
}
a {
	transition: .3s all ease;
	color: $primary;
	&:hover {
		text-decoration: none;
		color: $primary;
	}
}
h1, h2, h3, h4, h5,
.h1, .h2, .h3, .h4, .h5 {
	line-height: 1.4;
	color: $black;
	font-family: $font-secondary;
	font-weight: 400;
}

.text-primary {
	color: $primary!important;
}

.ftco-navbar-light {
	background: transparent!important;
	position: absolute;
	left: 0;
	right: 0;
	z-index: 3;
	top: 10px;
	@include media-breakpoint-down(md) {
		background: $black!important;
		top: 0;
		position: relative;
	}

	.navbar-brand {
		color: $white;
		@include media-breakpoint-up(md){
			color: $white;
		}
	}
	
	.navbar-nav {
		> .nav-item {
			> .nav-link {
				font-size: 16px;
				padding-top: .9rem;
				padding-bottom: .9rem;
				padding-left: 20px;
				padding-right: 20px;
				color: rgba(255,255,255,.8);
				font-weight: 400;
				&:hover {
					color: $white;
				}
				opacity: 1!important;
			}

			.dropdown-menu{
				border: none;
				background: $white;
				-webkit-box-shadow: 0px 10px 34px -20px rgba(0,0,0,0.41);
				-moz-box-shadow: 0px 10px 34px -20px rgba(0,0,0,0.41);
				box-shadow: 0px 10px 34px -20px rgba(0,0,0,0.41);
			}

			
			&.ftco-seperator {
				position: relative;
				margin-left: 20px;
				padding-left: 20px;
				@include media-breakpoint-down(md) {
					padding-left: 0;
					margin-left: 0;
				}
				&:before {
					position: absolute;
					content: "";
					top: 10px;
					bottom: 10px;
					left: 0;
					width: 2px;
					background: rgba($white, .05);
					@include media-breakpoint-down(md) {
						display: none;
					}
				}
			}
			&.cta {
				@include media-breakpoint-down(sm){
					margin-bottom: 20px;
				}
				> a {
					color: $white;
					border: 1px solid $primary;
					background: $primary;
					padding-top: .5rem;
					padding-bottom: .5rem;
					padding-left: 20px;
					padding-right: 20px;
					margin-top: 4px;
					@include border-radius(30px);
					span {
						display: inline-block;
						color: $white;
					}
					&:hover{
						background: $primary;
						border: 1px solid $primary;
					}
				}
				&.cta-colored {
					span {
						border-color: $primary;
					}
				}
			}
			&.active {
				> a {
					color: $primary;
				}
			}
		}
	}
	.navbar-toggler {
		border: none;
		color: rgba(255,255,255,.5)!important;
		cursor: pointer;
		padding-right: 0;
		text-transform: uppercase;
		font-size: 16px;
		letter-spacing: .1em;
	}
	
	&.scrolled  {
		position: fixed;
		right: 0;
		left: 0;
		top: 0;
		margin-top: -130px;
		background: $white!important;
		box-shadow: 0 0 10px 0 rgba(0,0,0,.1);
		.nav-item {
			&.active {
				> a {
					color: $primary!important;
				}
			}
			&.cta {
				> a {
					color: $white !important;
					background: $primary;
					border: none !important;
					padding-top: .5rem!important;
					padding-bottom: .5rem !important;
					padding-left: 20px;
					padding-right: 20px;
					margin-top: 6px !important;
					@include border-radius(30px);
					span {
						display: inline-block;
						color: $white !important;
					}
				}
				&.cta-colored {
					span {
						border-color: $primary;
					}
				}
			}
		}

		.navbar-nav {
			@include media-breakpoint-down(md) {
				background: none;
				border-radius: 0px;
				padding-left: 0rem!important;
				padding-right: 0rem!important;
			}
		}

		.navbar-nav {
			@include media-breakpoint-down(sm) {
				background: none;
				padding-left: 0!important;
				padding-right: 0!important;
			}
		}

		.navbar-toggler {
			border: none;
			color: rgba(0,0,0,.5)!important;
			border-color: rgba(0,0,0,.5)!important;
			cursor: pointer;
			padding-right: 0;
			text-transform: uppercase;
			font-size: 16px;
			letter-spacing: .1em;

		}
		.nav-link {
			padding-top: .9rem!important;
			padding-bottom: .9rem!important;
			color: $black!important;
			&.active {
				color: $primary!important;
			}
		}
		&.awake {
			margin-top: 0px;
			transition: .3s all ease-out;
		}
		&.sleep {
			transition: .3s all ease-out;	
		}

		.navbar-brand {
			color: $black;
		}
	}
}

.navbar-brand {
	font-weight: 700;
	font-size: 22px;
	text-transform: uppercase;
	letter-spacing: 1px;
	line-height: 1;
	small{
		font-size: 10px;
		letter-spacing: 6px;
		font-weight: 700;
		color: $primary;
	}
	i{
		color: $primary;
		font-size: 30px;
	}
}





.hero-wrap{
	width: 100%;
	height: calc(100vh - 117px);
	min-height: 700px;
	position: relative;
	&.hero-wrap-2{
		height: 600px;
	}
	.overlay{
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		content: '';
		opacity: 0;
		background: $black;
	}
}

.slider-text{
	color: $white;
	height: 700px;
	h1 {
		font-size: 60px;
		color: $white;
		line-height: 1.2;
		font-weight: 400;
		@include media-breakpoint-down(md) {
			font-size: 40px;
		}
	}
	p {
		font-size: 20px;
		line-height: 1.5;
		font-weight: 300;
		color: rgba(255,255,255,.8);
		a{
			color: $white;
			border-bottom: 2px solid rgba(255,255,255,.3);
			span{
				color: $white;
			}
		}
	}
	.breadcrumbs{
		text-transform: uppercase;
		font-size: 13px;
		letter-spacing: 1px;
		margin-bottom: 20px;
		z-index: 99;
		span{
			border-bottom: 2px solid rgba(255,255,255,.1);
			color: rgba(255,255,255,.7);
			a{
				color: $white;
			}
		}
	}
	.bread{
		font-weight: 400 !important;
	}
	.btn-outline-white{
		border: 1px solid rgba(255,255,255,.4) !important;
		span{
			color: $white;
		}
		&:hover{
			span{
				color: $primary;
			}
		}
	}
}

//OWL CAROUSEL
.owl-carousel {
	position: relative;
	.owl-item {
		opacity: .4;
		&.active {
			opacity: 1;
		}
	}
	
	.owl-nav {
		position: absolute;
		top: 50%;
		width: 100%;
		.owl-prev,
		.owl-next {
			position: absolute;
			transform: translateY(-50%);
			margin-top: -10px;
			color: $primary !important;
			@include transition(.7s);
			span {
				&:before {
					font-size: 30px;
				}
			}
			opacity: 0;
		}
		.owl-prev {
			left: 0;
		}
		.owl-next {
			right: 0;
		}
	}
	.owl-dots {
		text-align: center;
		.owl-dot {
			width: 10px;
			height: 10px;
			margin: 5px;
			border-radius: 50%;
			background: lighten($black, 90%);
			position: relative;
			&:after{
				position: absolute;
				top: -2px;
				left: -2px;
				right: 0;
				bottom: 0;
				width: 14px;
				height: 14px;
				content: '';
				border:1px solid lighten($black, 90%);
				@include border-radius(50%);
			}	
			&.active {
				background: lighten($black, 60%);
			}
		}
	}
	&:hover{
		.owl-nav{
			.owl-prev,
			.owl-next{
				opacity: 1;
			}
			.owl-prev {
				left: -25px;
			}
			.owl-next {
				right: -25px;
			}
		}
	}
}
.owl-custom-nav {
	float: right;
	position: relative;
	z-index: 10;
	.owl-custom-prev,
	.owl-custom-next {
		padding: 10px;
		font-size: 30px;
		background: #ccc;
		line-height: 0;
		width: 60px;
		text-align: center;
		display: inline-block;
	}
} 


.bg-light {
	background: lighten($black,98%)!important;
}

.bg-primary{
	background: $primary;
}


//BUTTON
.btn {
	cursor: pointer;
	@include border-radius(0px);
	// box-shadow: none!important;
	box-shadow: 0 5px 20px -5px rgba(0,0,0,.5);
	&:hover, &:active, &:focus {
		outline: none;
	}
	&.btn-primary {
		background: lighten($primary,0);
		border: 1px solid lighten($primary,0);
		color: $white;
		&:hover {
			border: 1px solid $primary;
			background: transparent !important;
			color :$primary;
		}
		&.btn-outline-primary {
			border: 1px solid $primary;
			background: transparent;
			color :$primary;
			&:hover {
				border: 1px solid transparent;
				background: $primary;
				color :$white;
			}
		}
	}
	&.btn-secondary {
		background: lighten($secondary,0);
		border: 1px solid lighten($secondary,0);
		color: $white;
		&:hover {
			border: 1px solid $secondary;
			background: transparent;
			color :$secondary;
		}
		&.btn-outline-secondary {
			border: 1px solid $secondary;
			background: transparent;
			color :$secondary;
			&:hover {
				border: 1px solid transparent;
				background: $secondary;
				color :$white;
			}
		}
	}
	&.btn-white {
		background: $white;
		border: 1px solid $white;
		color: $black;
		@include border-radius(0);
		// box-shadow: 0 5px 20px -5px rgba(0,0,0,.5) !important;
		&:hover {
			border: 1px solid $black;
			background: $black;
			color :$white;
		}
		&.btn-outline-white {
			border-color: rgba($white, .8);
			background: none;
			@include border-radius(0);
			border-width: 1px;
			color: $white;
			&:hover, &:focus, &:active {
				background: $white;
				border-color: $white;
				color: $primary;
			}
		}
	}
	&.btn-outline-black {
		border-color: rgba($black, 1);
		background: none;
		@include border-radius(2px);
		border-width: 1px;
		color: $black;
		&:hover, &:focus, &:active {
			background: $black;
			border-color: $black;
			color: $white;
		}
	}
}


.ftco-intro{
	margin-top: -80px;
	&.ftco-intro-2{
		margin-top: 0 !important;
	}
	.block-18 {
		display: block;
		width: 100%;
		padding: 30px;
		color: rgba(0,0,0,.7);
		&.color-1{
			background: $primary;
		}
		&.color-2{
			background: $secondary;
		}
		&.color-3{
			background: $tertiary;
		}
		.text {
			strong {
				font-size: 60px;
				color: $black;
				font-weight: 300;
			}
			span {
				display: block;
				color: $black;
				font-size: 24px;
			}
		}
	}
}

//SERVICES
.services{
	.icon{
		@include border-radius(50%);
		span{
			font-size: 50px;
			color: $primary;
		}
	}
	h3{
		font-size: 20px;
		margin-bottom: 20px;
	}
}


//CAUSE
.carousel-cause{
  &.owl-carousel .owl-stage-outer{
		padding-bottom: 2em;
		position: relative;
	}
}
.cause-entry{
	background: $white;
	border: 1px solid lighten($black,98%);
	-webkit-box-shadow: 0px 3px 66px -24px rgba(0,0,0,0.2);
	-moz-box-shadow: 0px 3px 66px -24px rgba(0,0,0,0.2);
	box-shadow: 0px 3px 66px -24px rgba(0,0,0,0.2);
	overflow: hidden;
	@include border-radius(4px);
	.img{
		display: block;
		height: 300px;
	}
	.text{
		@include media-breakpoint-up(md){
			margin-top: -40px !important;
			background: $white;
			width: 90%;
			margin: 0 auto;
		}
		h3{
			margin-bottom: 20px;
			a{
				color: $black;
			}
		}
		.custom-progress-success {
			background-color: lighten(#ccc, 15%);
			height: .3rem;
			border-radius: 4px;
			margin-bottom: 20px;
		}
	}
	span.donation-time{
		font-style: italic;
		color: lighten(#ccc, 1%);
	}
	span.fund-raised{
		color: $black;
	}
}

//ABOUT VIDEO
.ftco-section-3{
	padding: 8em 0;
	position: relative;
	.overlay{
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		content: '';
		opacity: .9;
		background: $primary;
		
	}
	.img{
		width: 100%;
	}
	.img-2{
		-webkit-box-shadow: 0px 3px 66px -24px rgba(0,0,0,0.2);
		-moz-box-shadow: 0px 3px 66px -24px rgba(0,0,0,0.2);
		box-shadow: 0px 3px 66px -24px rgba(0,0,0,0.2);
		@include border-radius(4px);
		@include media-breakpoint-down(sm){
			height: 200px;
			margin-bottom: 40px;
		}
	}
}
.volunteer{
	h3{
		color: $white;
	}
}
.volunter-form{
	.form-control {
		border: 2px solid rgba(255,255,255,.7);
		height: 58px!important;
		background: transparent!important;
		color: rgba(255,255,255,.8)!important;
		font-size: 18px;
		border-radius: 0px;
		box-shadow: none!important;
		&::-webkit-input-placeholder { /* Chrome/Opera/Safari */
		  color: rgba(255,255,255,.8);
		}
		&::-moz-placeholder { /* Firefox 19+ */
		  color: rgba(255,255,255,.8);
		}
		&:-ms-input-placeholder { /* IE 10+ */
		  color: rgba(255,255,255,.8);
		}
		&:-moz-placeholder { /* Firefox 18- */
		  color: rgba(255,255,255,.8);
		}
		&:focus, &:active {
			border-color: $white;
		}
	}
	textarea.form-control {
		height: inherit!important;
	}
}

//GALLERY
.gallery{
	width: 25%;
	position: relative;
	&:after{
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		content: '';
		background: rgba(0,0,0,.2);
		@include transition(.3s);
	}
	@include media-breakpoint-down(sm){
		width: 100%;
	}
	&.img{
		display: block;
		height: 300px;
	}
	.icon{
		width: 50px;
		height: 50px;
		background: rgba(255,255,255,.8);
		opacity: 0;
		@include border-radius(50%);
		@include transition(.3s);
		span{
			color: $primary;
		}
	}
	&:hover{
		.icon{
			opacity: 1;
		}
		&:after{
			opacity: 0;
		}
	}
}

//STAFF
.staff{
	padding: 25px;
	background: $white;
	-webkit-box-shadow: 0px 3px 66px -24px rgba(0,0,0,0.2);
	-moz-box-shadow: 0px 3px 66px -24px rgba(0,0,0,0.2);
	box-shadow: 0px 3px 66px -24px rgba(0,0,0,0.2);
	@include border-radius(4px);
	@include media-breakpoint-down(sm){
		margin-bottom: 30px;
	}
	.info{
		width: calc(100% - 110px);
		h3{
			font-size: 24px;
			a{
				color: $black;
			}
		}
		span.position{
			display: block;
			margin-bottom: 15px;
			font-size: 14px;
		}
	}
	.img{
		width: 110px;
		height: 105px;
		border: 4px solid lighten($black,94%);
		@include border-radius(50%);
	}
	.text{
		p{
			margin-bottom: 0;
			span{
				font-size: 20px;
				font-weight: 400;
				color: $quarternary;				
			}
			a{
				text-decoration: underline;
			}
		}
	}
}

.ftco-social{
	a{
		width: 30px;
		height: 30px;
		display: inline-block;
		margin-right: 2px;
		background: lighten($primary,40%);
		@include border-radius(50%);
		span{
			color: $primary;
		}
	}
}


//////////////////
.ftco-section-2{
	position: relative;
	display: block;
	width: 100%;
	overflow-x: hidden;
}
.services-wrap{
	display: block;
	width: 100%;
	position: relative;
	background: rgba(255,255,255,.8);
	margin-bottom: 1px;
	padding: 30px 40px 30px 50px;
	&:first-child{
		margin-top: 1px;
	}
	&:after{
		position: absolute;
		top: 0;
		left: 100%;
		bottom: 0;
		content: '';
		width: 360%;
		background: rgba(255,255,255,.8);
	}
	h2{
		color: $primary;
		font-size: 24px;
	}
	p{
		color: $darken;
		margin-bottom: 0;
	}
	.icon{
		position: absolute;
		top: 0;
		bottom: 0;
		left: 15px;
		span{
			font-size: 20px;
			color: lighten($primary,20%);
			&:first-child{
				opacity: 0;
			}
			&:last-child{
				opacity: 1;
			}
		}
	}
	&:hover{
		margin-left: -100px;
		.icon{
			span{
				&:first-child{
					opacity: 1;
					margin-left: 5px;
				}
				&:last-child{
					opacity: 0;
				}
			}
		}
	}
}

//ACCORDION
#accordion{
	.card{
		margin-bottom: 5px;
		border: 1px solid lighten($primary,40%);
		@include border-radius(0);
		.card-header{
			padding: 0;
			background: transparent;
			border-bottom: 1px solid lighten($black,90%);
			@include border-radius(0);
			a{
				padding: 12px 20px;
				display: block;
				font-size: 20px;
				font-weight: 400;
				color: $black;
				span{
					float: right;
					i{
						color: lighten($primary,30%);
					}
				}
				small{
					padding: 4px 10px;
					margin-left: 10px;
					background: lighten($primary,40%);
					color: $primary;
					@include border-radius(30px);
				}
			}
		}
	}
	.card-body{
		border: 1px solid transparent;
		p{
		}
	}
	[aria-expanded="false"] > .expanded, [aria-expanded="true"] > .collapsed {display: none;}
}


//### .block-3
.block-3 {
	@include media-breakpoint-up(md){
		margin-bottom: 7em;
	}
	.text, .image {
		width: 100%;
		padding: 10% 7%;
		display: block;
		@include media-breakpoint-up(md) {
			width: 50%;
			padding: 10% 7%;	
		}
	}
	.text {
		.subheading {
			font-size: 13px;
			text-transform: uppercase;
			letter-spacing: .1em;
		}
		.heading {
			font-size: 30px;
			margin-bottom: 30px;
			a{
				color: $black;
			}
		}
		p {
			&:last-child {
				margin-bottom: 0;
			}
		}
	}
	.image {
		background-size: cover;
		background-position: center center;
		background-repeat: no-repeat;
		position: relative;
		&:after{
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			content: '';
			box-shadow: 20px 20px 0 0 lighten($primary,25%);
		}
		&.image-2{
			&:after{
				position: absolute;
				top: 0;
				left: 0;
				right: 0;
				bottom: 0;
				content: '';
				box-shadow: -20px 20px 0 0 lighten($primary,25%);
			}
		}
		@include media-breakpoint-down(sm) {
			height: 300px;
		}
	}
}

//### .block-5
.block-5 {
	overflow: hidden;
	background-size: cover;
	background-repeat: no-repeat;
	background-position: center center;
	height: 400px;
	position: relative;
	display: block;

	&:before {
		content: '';
      position: absolute;
      top: 0;
    	right: 0;
    	bottom: 0;
   	left: 0;
    	background: -moz-linear-gradient(top, transparent 0%, transparent 18%, rgba(0,0,0,0.8) 99%, rgba(0,0,0,0.8) 100%);
    	background: -webkit-linear-gradient(top, transparent 0%, transparent 18%, rgba(0,0,0,0.8) 99%, rgba(0,0,0,0.8) 100%);
    	background: linear-gradient(to bottom, transparent 0%, transparent 18%, rgba(0,0,0,0.8) 99%, rgba(0,0,0,0.8) 100%);
    	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#00000000', endColorstr='#cc000000',GradientType=0 );
    	opacity: .8;
	}
	.text {
		position: absolute;
		bottom: 0;
		right: 0;
		left: 0;
		padding: 20px 20px 10px 20px;
		transition: .8s all ease;

		&:before {
			bottom: 0;
			left: 0;
			right: 0;
			z-index: 1;
			background: $white;	
			visibility: hidden;
			opacity: 0;
			height: 0;
			position: absolute;
			content: "";
			@include transition(.3s);
		}
		.heading, .subheading, .post-meta, .excerpt, .price{
			z-index: 2;
			position: relative;
		}
		.subheading {
			color: $white;
			text-transform: uppercase;
			letter-spacing: .1em;
			font-size: 12px;
			margin-bottom: 5px;
			opacity: .6;
		}
		.heading {
			color: $white;
			margin: 0 0 10px 0;
			padding: 0;
			font-weight: bold;
			font-size: 24px;
			line-height: 1.2;
			font-weight: 400;
		}
		.post-meta {
			line-height: 1.4;
			color: $white;
			font-size: 14px;
			// text-transform: uppercase;
			span {
				display: inline-block;
				margin-right: 10px;
				margin-bottom: 10px;
				opacity: .6;

			}
		}
		.price{
			color: $white;
		}
		.excerpt {
			line-height: 1.4;
			color: $white;
		}
	}
	&:hover, &:focus {
		.text {
			
			&:before {
				visibility: visible;
				opacity: 1;
				height: 100%;
				background:$white;
			}
			.heading, .subheading, .post-meta, .price, .star-rate {
				color: $black;
			}
			
		}
	}
}

// USEFUL CODE
.aside-stretch{
	background: lighten($primary,10%);
	&:after{
		position: absolute;
		top: 0;
		right: 100%;
		bottom: 0;
		content: '';
		width: 360%;
		background: lighten($primary,10%);
		// background: #333644;
	}
	@include media-breakpoint-down(sm){
		background: transparent;
		&:after{
			background: transparent;
			display: none;
		}
	}
}


.form-control {
	height: 58px!important;
	background: $white!important;
	color: $black!important;
	font-size: 18px;
	border-radius: 0px;
	box-shadow: none!important;
	&:focus, &:active {
		border-color: $black;
	}
}
textarea.form-control {
	height: inherit!important;
}
.ftco-vh-100 {
  height: 100vh;
  @include media-breakpoint-down(lg) {
  	height: inherit;
  	padding-top: 5em;
  	padding-bottom: 5em;
  }
}
.ftco-vh-75 {
  height: 75vh;
  min-height: 700px;
  @include media-breakpoint-down(lg) {
  	min-height: 700px;
  	height: inherit;
  	padding-top: 5em;
  	padding-bottom: 5em;
  }
}


.ftco-tab-nav {
	padding: 0;
	margin: 0;
	display: inline-block!important;
	@include media-breakpoint-down(sm) {
		display: block!important;
		margin-bottom: 10px;
		width: 100%!important;
	}
	li {
		padding: 0;
		margin: 0 5px;
		display: inline-block!important;
		@include media-breakpoint-down(sm) {
			display: block!important;
			margin-bottom: 10px;
			width: 100%!important;
		}
		a {
			text-transform: uppercase;
			font-size: 14px;
			letter-spacing: .2em;
			color: #ccc;
			border: 2px solid #ccc;
			border-radius: 0!important;
			&.active {
				background: none!important;
				color: darken(#ccc, 100%)!important;
				border: 2px solid $black;
			}
		}
		
	}
}

.ftco-animate {
	opacity: 0;
	visibility: hidden;
}

.bg-primary {
	background: $primary!important;
}
.ftco-section {
	.ftco-sub-title {
		font-size: 16px;
		text-transform: uppercase;
		letter-spacing: .5em;
		color: lighten(#ccc, 3%);
		font-family: $font-family-sans-serif;
		font-weight: 300;
	}
	.ftco-primary-title {
		margin-top: 0;
		margin-bottom: 30px;
	}
}



//ABOUT
.media-custom{
	background: $white;
	.media-body{
		.name{
			font-weight: 500;
			font-size: 16px;
			margin-bottom: 0;
			color: $primary;
		}
		.position{
			font-size: 13px;
			color: lighten($black, 85%);
		}
	}
}


.about-author{
	img{
	}
	.desc{
		h3{
			font-size: 24px;
		}
	}
	.bio{

	}
}


.ftco-section {
	padding: 7em 0;
	position: relative;
	@include media-breakpoint-down(sm){
		padding: 6em 0;
	}
}

.ftco-bg-dark {
	background: #3c312e;
}


.ftco-footer {
	font-size: 16px;
	padding: 7em 0;
	background: #252525;
	.ftco-footer-logo {
		text-transform: uppercase;
		letter-spacing: .1em;
	}
	.ftco-footer-widget {
		h2 {
			font-weight: normal;
			color: $white;
			margin-bottom: 40px;
			font-size: 18px;
			font-weight: 400;
			position: relative;
			text-transform: uppercase;
			letter-spacing: 1px;
			&:after{
				position: absolute;
				bottom: -15px;
				left: 0;
				content: '';
				width: 70px;
				height: 1px;
				background: $white;
			}
		}
		ul{
			li{
				a{
					span{
						color: $white;
					}
				}
			}
		}
		.btn-primary{
			background: $white !important;
			border: 2px solid $white !important;
			&:hover{
				background: $white;
				border: 2px solid $white !important;
			}
		}
	}
	p {
		color: rgba($white, .7);
	}
	a {
		color: rgba($white, .7);
		&:hover {
			color: $white;
		}
	}
	.ftco-heading-2 {
		font-size: 17px;
		font-weight: 400;
		color: $black;
	}
	.block-21 {
		.text {
			.heading {
				font-size: 18px;
				a {
					color: rgba(255,255,255,.8);
					&:hover, &:active, &:focus {
						color: $primary;
					}
				}
			}
			.meta {
				> div {
					display: inline-block;
					font-size: 12px;
					margin-right: 5px;
					a {
						color: lighten($black, 50%);
					}
				}
			}
		}
	}
}


.ftco-footer-social {
	li {
		list-style: none;
		margin: 0 10px 0 0;
		display: inline-block;
		a {
			height: 50px;
			width: 50px;
			display: block;
			float: left;
			background: rgba($white, .05);
			border-radius: 50%;
			position: relative;
			span {
				position: absolute;
				font-size: 26px;
				top: 50%;
				left: 50%;
				transform: translate(-50%, -50%);
			}
			&:hover {
				color: $white;
			}
		}
	}
}
.footer-small-nav {
	> li {
		display: inline-block;
		a {
			margin: 0 10px 10px 0;
			&:hover, &:focus {
				color: $primary;
			}
		}
	}
}
.media {
	.ftco-icon {
		width: 100px;
		span {
			color: $primary;
		}
	}
}
.ftco-media {
	background: $white;
	border-radius: 0px;
	.heading {
		font-weight: normal;
	}
	&.ftco-media-shadow {
		padding: 40px;
		background: $white;
		box-shadow: 0 10px 50px -15px rgba(0,0,0,.3);
		transition: .2s all ease;
		position: relative;
		top: 0;
		&:hover, &:focus {
			top: -3px;
			box-shadow: 0 10px 70px -15px rgba(0,0,0,.3);
		}
	}
	.icon {
		font-size: 50px;
		display: block;
		color: $primary;
	}
	&.text-center {
		.ftco-icon {
			margin: 0 auto;
		}
	}
}
.ftco-overflow-hidden {
	overflow: hidden;
}

.padding-top-bottom {
	padding-top: 120px;
	padding-bottom: 120px;
}

// Map

#map {
	height: 400px;
	width: 100%;
	@include media-breakpoint-down(md) {
		height: 300px;
	}
}


@-webkit-keyframes pulse {
  0% {
    -webkit-box-shadow: 0 0 0 0 rgba($primary, 0.4);
  }
  70% {
      -webkit-box-shadow: 0 0 0 30px rgba($primary, 0);
  }
  100% {
      -webkit-box-shadow: 0 0 0 0 rgba($primary, 0);
  }
}
@keyframes pulse {
  0% {
    -moz-box-shadow: 0 0 0 0 rgba($primary, 0.4);
    box-shadow: 0 0 0 0 rgba($primary, 0.4);
  }
  70% {
      -moz-box-shadow: 0 0 0 30px rgba($primary, 0);
      box-shadow: 0 0 0 30px rgba($primary, 0);
  }
  100% {
      -moz-box-shadow: 0 0 0 0 rgba($primary, 0);
      box-shadow: 0 0 0 0 rgba($primary, 0);
  }
}

.heading-section{
	.subheading{
		font-size: 14px;
		display: block;
		margin-bottom: 10px;
	}
	h2{
		font-size: 34px;
		font-weight: 400;
		text-transform: uppercase;
		@include media-breakpoint-down(sm){
			font-size: 28px;
		}
	}
	&.heading-section-white{
		.subheading{
			color: rgba(255,255,255,.9);
		}
		h2{
			font-size: 30px;
			color: $white;
		}
		p{
			color: rgba(255,255,255,.9);
		}
	}
}

//COVER BG
.hotel-img,
.hero-wrap,
.img,
.blog-img,
.user-img{
	background-size: cover;
	background-repeat: no-repeat;
	background-position: center center;
}


.img-about{
	@include media-breakpoint-down(sm){
		height: 400px;
		margin-bottom: 30px;
	}
}


// magnific pop up

.image-popup {
	cursor: -webkit-zoom-in;
	cursor: -moz-zoom-in;
	cursor: zoom-in;
}
.mfp-with-zoom .mfp-container,
.mfp-with-zoom.mfp-bg {
  opacity: 0;
  -webkit-backface-visibility: hidden;
  -webkit-transition: all 0.3s ease-out; 
  -moz-transition: all 0.3s ease-out; 
  -o-transition: all 0.3s ease-out; 
  transition: all 0.3s ease-out;
}

.mfp-with-zoom.mfp-ready .mfp-container {
    opacity: 1;
}
.mfp-with-zoom.mfp-ready.mfp-bg {
    opacity: 0.8;
}

.mfp-with-zoom.mfp-removing .mfp-container, 
.mfp-with-zoom.mfp-removing.mfp-bg {
  opacity: 0;
}



#section-counter{
	position: relative;
	z-index: 0;
}

//blocks 
.block-20 {
	overflow: hidden;
	background-size: cover;
	background-repeat: no-repeat;
	background-position: center center;
	position: relative;
	display: block;
	width: 100%;
	height: 270px;
}
.blog-entry{
	border: 1px solid lighten($black,95%);
	background:$white;
	overflow: hidden;
	@include border-radius(4px);
	-webkit-box-shadow: 0px 3px 66px -24px rgba(0,0,0,0.2);
	-moz-box-shadow: 0px 3px 66px -24px rgba(0,0,0,0.2);
	box-shadow: 0px 3px 66px -24px rgba(0,0,0,0.2);
	@include media-breakpoint-up(md){
		margin-bottom: 30px;
	}
	@include media-breakpoint-down(sm){
		margin-bottom: 30px;
	}
	.text {
		position: relative;
		@include media-breakpoint-up(md){
			width: 90%;
			margin: 0 auto;
			margin-top: -40px;
			background: $white;
		}
		.heading {
			font-size: 20px;
			margin-bottom: 16px;
			font-weight: 400;
			a {
				color: $black;
				&:hover, &:focus, &:active {
					color: $primary;
				}
			}
		}
		.time-loc{
			font-size: 14px;
			span{
				color: lighten($darken,30%);
				i{
					color: $primary;
				}
			}
		}
	}
	.meta {
		> div {
			display: inline-block;
			margin-right: 5px;
			margin-bottom: 5px;
			font-size: 15px;
			a {
				color: lighten($black, 50%);
				font-size: 15px;
				&:hover {
					color: lighten($black, 40%);
				}
			}
		}
	}
}


.block-23 {
	ul {
		padding: 0;
		li {
			
			&, > a {
				display: table;
				line-height: 1.5;
				margin-bottom: 15px;
			}
			span{
				color: rgba($white, .7);
			}
			.icon, .text {
				display: table-cell;
				vertical-align: top;
			}
			.icon {
				width: 40px;
				font-size: 18px;
				padding-top: 2px;
				color: rgba($white, 1);
			}
			
		}
	}
}

.block-6 {
	margin-bottom: 40px;
	.icon {
		span {
			&:before {
			}
		}
	}
	.media-body {
		.heading {

		}
		p {
			font-size: 16px;
		}
	}
} 

//### .block-10 
.block-10 {
	.chef-img{
		height: 400px;
	}
	.person-info {
		height: 75px;
		span {
			display: block;
		}
		.name {
			font-size: 20px;
			color: $black;
			font-weight: 400;
		}
		.position {
			font-size: 14px;
		}
	}
}

//### .block-16
.block-16 {
	figure {
		position: relative;
		.play-button {
			position: absolute;
			top: 50%;
			left: 50%;
			transform: translate(-50%, -50%);
			font-size: 40px;
			width: 90px;
			height: 90px;
			background: $white;
			display: block;
			border-radius: 50%;
			opacity: .7;
			&:hover {
				opacity: 1;
			}
			> span {
				position: absolute;
				left: 55%;
				top: 50%;
				transform: translate(-50%, -50%);
			}
		}
	}
}

.block-17 {
	background: $white;
	overflow: hidden;
	@include border-radius(2px);
	form {
		.fields {
			width: calc(100% - 80px);
			position: relative;
			.one-third {
				width: 50%;
				background: $white;
				&:first-child {
					// padding-left: 0;
				}
				&:last-child {
					border-right: none;
					border-left: 1px solid rgba(0,0,0,.1);
				}
				label{
					font-weight: 700;
					color: $black;
				}
			}
			.form-control {
				box-shadow: none!important;
				border: transparent;
				background: $white !important;
				color: lighten($black,30%) !important;
				border: 2px solid rgba(0,0,0,.1);
				font-size: 14px;
				width: 100%;
				height: 70px !important;
				bordeR: 1px solid transparent;
				padding: 10px 30px;
				@include border-radius(0);
				&::-webkit-input-placeholder { /* Chrome/Opera/Safari */
				  color: lighten($black,30%);
				}
				&::-moz-placeholder { /* Firefox 19+ */
				  color: lighten($black,30%);
				}
				&:-ms-input-placeholder { /* IE 10+ */
				  color: lighten($black,30%);
				}
				&:-moz-placeholder { /* Firefox 18- */
				  color: lighten($black,30%);
				}
			}
			.icon {
				position: absolute;
				top: 50%;
				right: 30px;
				font-size: 14px;
				transform: translateY(-50%);
				color: rgba($black,.7);
				@include media-breakpoint-down(sm) {
					right: 10px;
				}
			}
			.textfield-search, .select-wrap {
			}
			.textfield-search {
				input {

				}
			}
			.select-wrap {
				position: relative;
				select {
					appearance: none;
				}
			}
		}
		.search-submit {
			width: 160px;
			background: $primary;
			border: 1px solid $primary;
			color: $white;
			padding: 12px 10px;
			@include media-breakpoint-down(sm){
				width: 80px;
			}
			@include border-radius(0);
			&:hover{
				background: $primary !important;
				color: $white !important;
				border: 1px solid $primary !important;
			}
		}
	}
}

//### .block-18 
.block-18 {
	.icon, .text {
	}
	.icon {
		> span {
			font-size: 40px;
		}
	}
	.text {
		strong {
			font-size: 30px;
		}
		span {
			display: block;
		}
	}
}


.block-27 {
	ul {
		padding: 0;
		margin: 0;
		li {
			display: inline-block;
			margin-bottom: 4px;
			font-weight: 400;
			a,span {
				color: $primary;
				text-align: center;
				display: inline-block;
				width: 40px;
				height: 40px;
				line-height: 40px;
				border-radius: 50%;
				border: 1px solid lighten($primary,25%);
			}
			&.active {
				a, span {
					background: lighten($primary,25%);
					color: $primary;
					border: 1px solid transparent;
				}
			}
		}
	}
}

.block-8 {
	.accordion-item {
		.heading {
			font-size: 16px;
			font-weight: 400;
			padding: 10px 0;
			> a {
				padding-left: 35px;
				position: relative;
				color: $black;
				&:before {

					width: 20px;
					height: 20px;
					line-height: 18px;
					border: 1px solid #ccc;
					text-align: center;
					font-size: 18px;
					top: .1em;
					left: 0;
				}
				&[aria-expanded="true"] {
					&:before {
						font-family: 'icomoon';
						position: absolute;
						content: "\e316";
						transition: .3s all ease;
						background: $primary;
						color: $white;
						border: 1px solid $primary;
					}
				}
				&[aria-expanded="false"] {
					&:before {
						content: "\e313";
						color: #ccc;	
						font-family: 'icomoon';
						position: absolute;
				
						transition: .3s all ease;
					}
				}
				
			}
		}
		.body-text {
			font-size: 16px;
			padding: 5px 0;
			padding-left: 30px;
		}
	}
}

//### .block-4
.block-4 {
	.nonloop {
		.owl-stage {
			padding-bottom: 2em;
		}
		.owl-item {
			box-shadow: 0 7px 20px -5px rgba(0,0,0,.2);	
		}
		.owl-nav {
			z-index: 2;
			position: absolute;
			width: 100%;
			bottom: -2px;
			.owl-prev, .owl-next {
				opacity: .2;
				transition: .3s all ease;
				&:hover {
					opacity: 1;
				}
				&.disabled {
					display: none;
				}
				position: absolute;
				span {
					font-size: 30px;
				}
			}
			.owl-prev {
				left: 50px;

			}
			.owl-next {
				right: 50px;
			}
		}
		.owl-dots {
			bottom: -40px;
			position: absolute;
			width: 100%;
			text-align: center;
			.owl-dot {
				display: inline-block;
				width: 8px;
				height: 8px;
				background: #ccc;
				border-radius: 50%;
				margin-right: 10px;
				margin-bottom: 10px;
				transition: .3s all ease;
				&.active {
					
					background: $primary;
				}
			}
		}
	}
}

.contact-section {
	.contact-info{
		p{
			a{
				color: $secondary;
			}
			span{
				color: $black;
			}
		}
	}
}
.block-9 {

	.form-control {
		outline: none!important;
		box-shadow: none!important;
		font-size: 15px;
	}
	#map {
	}
}


//### .block-21
.block-21 {
	.blog-img{
		display: block;
		height: 80px;
		width: 80px;
	}
	.text {
		width: calc(100% - 100px);
		.heading {
			font-size: 18px;
			a {
				color: $black;
				&:hover, &:active, &:focus {
					color: $primary;
				}
			}
		}
		.meta {
			> div {
				display: inline-block;
				font-size: 12px;
				margin-right: 5px;
				a {
					color: lighten($black, 50%);
				}
			}
		}
	}
}

.custom-pagination {
	width: 100%;
	text-align: center;
	display: inline-block;
	li {
		display: inline-block;
	}
	.prev, .next {
		a {
			font-size: 20px!important;
			line-height: 38px!important;
		}
	}
	li, .prev, .next {
		a {
			width: 40px;
			height: 40px;
			line-height: 40px;
			padding: 0;
			margin: 0;
			border-radius: 50%!important;
			font-size: 16px;
		}
		&.active {
			a {
				display: block;
				width: 40px;
				height: 40px;
				line-height: 40px;
				padding: 0;
				margin: 0;
				border-radius: 50%!important;
				font-size: 16px;
				background: $primary;
				color: $white;
				&:hover, &:focus {
					color: $white;
				}
			}	
		}
	}
	.prev {
		float: left;
	}
	.next {
		float: right;
	}
}

/* Blog*/
.post-info {
	font-size: 12px;
	text-transform: uppercase;
	font-weight: bold;
	color: $white;
	letter-spacing: .1em;
	> div {
		display: inline-block;

		.seperator {
			display: inline-block;
			margin: 0 10px;
			opacity: .5;
		}
	}
}

.tagcloud {
	a {
		text-transform: uppercase;
		display: inline-block;
		padding: 4px 10px;
		margin-bottom: 7px;
		margin-right: 4px;
		border-radius: 4px;
		color: $black;
		border: 1px solid #ccc;
		font-size :11px;
		&:hover {
			border: 1px solid #000;
		}
	}
}

.comment-form-wrap {
	clear: both;
}

.comment-list {
	padding: 0;
	margin: 0;
	.children {
		padding: 50px 0 0 40px;
		margin: 0;
		float: left;
		width: 100%;
	}
	li {
		padding: 0;
		margin: 0 0 30px 0;
		float: left;
		width: 100%;
		clear: both;
		list-style: none;
		.vcard {
			width: 80px;
			float: left;
			img {
				width: 50px;
				border-radius: 50%;
			}
		}
		.comment-body {
			float: right;
			width: calc(100% - 80px);
			h3 {
				font-size: 20px;
			}
			.meta {
				text-transform: uppercase;
				font-size: 13px;
				letter-spacing: .1em;
				color: #ccc;
			}
			.reply {
				padding: 5px 10px;
				background: lighten($black, 90%);
				color: $black;
				text-transform: uppercase;
				font-size: 11px;
				letter-spacing: .1em;
				font-weight: 400;
				border-radius: 4px;
				&:hover {
					color: $white;
					background: lighten($black, 0%);
				}
			}
		}
	}
}

.search-form {
	background: lighten($black, 95%);
	padding: 10px;

	.form-group {
		position: relative;
		input {
			padding-right: 50px;
		}
	}
	.icon {
		position: absolute;
		top: 50%;
		right: 20px;
		transform: translateY(-50%);
	}
}


//SIDEBAR SEARCH
.sidebar-wrap{
	padding: 20px;
	border: 1px solid lighten($black,90%);
	margin-bottom: 30px;
	.heading{
		font-size: 18px;
		text-transform: uppercase;
	}
	.fields {
		width: 100%;
		position: relative;
		.form-control {
			box-shadow: none!important;
			border: transparent;
			background: $white !important;
			color: lighten($black,30%) !important;
			border: 1px solid lighten($black,90%);
			font-size: 14px;
			width: 100%;
			height: 52px !important;
			padding: 10px 20px;
			@include border-radius(0);
			&::-webkit-input-placeholder { /* Chrome/Opera/Safari */
			  color: lighten($black,30%);
			}
			&::-moz-placeholder { /* Firefox 19+ */
			  color: lighten($black,30%);
			}
			&:-ms-input-placeholder { /* IE 10+ */
			  color: lighten($black,30%);
			}
			&:-moz-placeholder { /* Firefox 18- */
			  color: lighten($black,30%);
			}
		}
		.icon {
			position: absolute;
			top: 50%;
			right: 30px;
			font-size: 14px;
			transform: translateY(-50%);
			color: rgba($black,.7);
			@include media-breakpoint-down(sm) {
				right: 10px;
			}
		}
		.textfield-search, .select-wrap {
		}
		.textfield-search {
			input {

			}
		}
		.select-wrap {
			position: relative;
			select {
				appearance: none;
			}
		}
	}
	.form-group{
		.btn{
			width: 100%;
			display: block !important;
			@include border-radius(2px);
		}
	}
}

// sidebar

.sidebar-box {
	margin-bottom: 30px;
	padding: 25px;
	font-size: 15px;
	width: 100%;
	
	float: left;
	
	background: $white;
	*:last-child {
		margin-bottom: 0;
	}
	h3 {
		font-size: 18px;
		margin-bottom: 15px;
	}
}

.categories, .sidelink {
	li {
		position: relative;
		margin-bottom: 10px;
		padding-bottom: 10px;
		border-bottom: 1px dotted gray('300');
		list-style: none;
		&:last-child {
			margin-bottom: 0;
			border-bottom: none;
			padding-bottom: 0;
		}
		a {
			display: block;
			span {
				position: absolute;
				right: 0;
				top: 0;
				color: #ccc;
			}
		}
		&.active {
			a {
				color: $black;
				font-style: italic;
			}
		}
	}
}



#ftco-loader {
	position: fixed;
	width:  96px;
	height: 96px;
	left:  50%;
	top: 50%;
	transform: translate(-50%, -50%);
	background-color: rgba(255,255,255,0.9);
	box-shadow: 0px 24px 64px rgba(0,0,0,0.24);
	border-radius:16px;
	opacity: 0; 
	visibility: hidden;
	transition: opacity .2s ease-out, visibility 0s linear .2s;
	z-index:1000;
}

#ftco-loader.fullscreen {
	padding:  0;
	left:  0;
	top:  0;
	width:  100%;
	height: 100%;
	transform: none;
	background-color: #fff;
	border-radius: 0;
	box-shadow: none;
}

#ftco-loader.show {
	transition: opacity .4s ease-out, visibility 0s linear 0s;
	visibility: visible;
	opacity: 1;
}

#ftco-loader .circular {
  animation: loader-rotate 2s linear infinite;
  position: absolute;
  left:  calc(50% - 24px);
  top:  calc(50% - 24px);
  display: block;
  transform: rotate(0deg);
}

#ftco-loader .path {
  stroke-dasharray: 1, 200;
  stroke-dashoffset: 0;
  animation: loader-dash 1.5s ease-in-out infinite;
  stroke-linecap: round;
}

@keyframes loader-rotate {
  100% {
    transform: rotate(360deg);
  }
}

@keyframes loader-dash {
  0% {
    stroke-dasharray: 1, 200;
    stroke-dashoffset: 0;
  }
  50% {
    stroke-dasharray: 89, 200;
    stroke-dashoffset: -35px;
  }
  100% {
    stroke-dasharray: 89, 200;
    stroke-dashoffset: -136px;
  }
}